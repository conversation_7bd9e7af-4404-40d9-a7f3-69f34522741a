import React from 'react';
import { FocusAwareStatusBar, View, H3, Text, Button } from '../ui';

interface ErrorScreenProps {
  title: string;
  message: string;
  onRetry: () => void;
}

export const ErrorScreen: React.FC<ErrorScreenProps> = ({
  title,
  message,
  onRetry,
}) => {
  return (
    <View className="flex-1 items-center justify-center p-4 gap-4 bg-bg-canvas-light dark:bg-bg-canvas-dark">
      <FocusAwareStatusBar />
      <H3 className="text-center">{title}</H3>
      <Text className="text-center text-fg-muted-light dark:text-fg-muted-dark">
        {message}
      </Text>

      <Button label='Try again' onPress={onRetry}/>
    </View>
  );
};
