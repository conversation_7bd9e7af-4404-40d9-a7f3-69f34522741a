import 'react-native-get-random-values';

import { Env } from '@env';
import { SimpleLineIcons } from '@expo/vector-icons';
import { BottomSheetTextInput } from '@gorhom/bottom-sheet';
import * as Location from 'expo-location';
import { useColorScheme } from 'nativewind';
import React, { useEffect, useState } from 'react';
import { TextInput, TouchableOpacity, View } from 'react-native';
import {
  type AddressComponent,
  GooglePlacesAutocomplete,
  type GooglePlacesAutocompleteRef,
  type Point,
} from 'react-native-google-places-autocomplete';

import { getCurrentLatLng, googlePlacesAutocompleteDefaultProps } from '@/lib';
import { type LocationType } from '@/types';

import { colors, LocationIcon } from '../ui';

export interface ILocationPicker {
  close: () => void;
  present: () => void;
}

type LocationInputType = {
  city: string;
  state: string;
  street: string;
  country: string;
  address: string;
  landmark?: string;
};

export interface LocationInputProps {
  onSelectLocation: (location: LocationInputType, coordinates: Point) => void;
  defaultValue?: LocationType;
  handleNext?: (e?: React.FormEvent<HTMLFormElement> | undefined) => void;
  isNextDisabled?: boolean;
  isEventLocation?: boolean;
  navigation?: any;
  onBlur?: () => void;
  onFocus?: () => void;
  borderRadius?: number;
  hasCurrentLocationIcon?: boolean;
  className?: string;
  shouldDisplayCityAsLocationName?: boolean;
  textInputHeight?: number;
  useBottomSheetInput?: boolean;
}

const LocationInput: React.FC<LocationInputProps> = (props) => {
  const {
    onSelectLocation,
    defaultValue,
    onBlur,
    onFocus,
    borderRadius = 8,
    hasCurrentLocationIcon = true,
    className,
    shouldDisplayCityAsLocationName,
    textInputHeight = 48,
    useBottomSheetInput,
  } = props;
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  const googlePlacesAutocompleteRef =
    React.useRef<GooglePlacesAutocompleteRef>(null);

  const [coordinates, setCoordinates] = useState<Point>({
    lat: defaultValue?.coordinates.lat || 0,
    lng: defaultValue?.coordinates.lng || 0,
    latitude: defaultValue?.coordinates.lat || 0,
    longitude: defaultValue?.coordinates.lng || 0,
  });

  const [location, setLocation] = useState<LocationInputType>({
    city: defaultValue?.city || '',
    state: defaultValue?.state || '',
    street: defaultValue?.street || '',
    country: defaultValue?.country || '',
    address: defaultValue?.address || '',
    landmark: defaultValue?.landmark || '',
  });

  const [locationName, setLocationName] = useState<string>(
    shouldDisplayCityAsLocationName
      ? (defaultValue?.city ?? '')
      : defaultValue?.landmark
        ? defaultValue.landmark
        : (defaultValue?.address ?? '')
  );
  // console.log('🚀 ~ _locationName:', _locationName);

  const [_searchFocused, setSearchFocused] = useState<boolean>(false);
  const [isPlacePicked, setIsPlacePicked] = useState<boolean>(false);

  const extractAddressInfo = (
    addrComponents: AddressComponent[],
    address: string,
    name?: string,
    coordinates?: Point
  ) => {
    let streetNumber = '';
    let city = '';
    let route = '';
    const locationObject = {
      city: '',
      state: '',
      street: '',
      country: '',
      address: address ?? '',
      landmark: name ?? '',
    };
    addrComponents.forEach((item) => {
      if (item.types.includes('locality')) {
        city = item.long_name || 'N/A';
      } else if (item.types.includes('administrative_area_level_1')) {
        locationObject.state = item.long_name;
      } else if (item.types.includes('street_number')) {
        streetNumber = item.long_name;
      } else if (item.types.includes('route')) {
        locationObject.street =
          (locationObject.street ? locationObject.street + ' ' : '') +
          item.long_name;
      } else if (item.types.includes('country')) {
        locationObject.country = item.long_name;
      }
    });

    // If streetNumber is not available, set a default value or handle the situation
    if (!streetNumber) {
      streetNumber = 'N/A';
    }

    locationObject.street = `${streetNumber} ${route}`.trim();

    if (!locationObject.address) {
      const { country, state, street } = locationObject;
      locationObject.address = `${street ? `${street}, ` : ''}${
        city ? `${city}, ` : ''
      }${state ? `${state}, ` : ''}${country ? `${country} ` : ''}`;
    }

    if (!city) {
      city = 'N/A';
    }
    locationObject.city = city;
    onSelectLocation(locationObject, coordinates!);
    setLocation(locationObject);
  };

  const handleGetLocation = async () => {
    const latlng = await getCurrentLatLng();
    if (!latlng) return;

    const { latitude, longitude } = latlng;
    const coords = { lat: latitude, lng: longitude };

    const locationObj: LocationType = {
      coordinates: coords,
      landmark: 'N/A',
      city: 'N/A',
      state: 'N/A',
      street: 'N/A',
      country: 'N/A',
      address: 'N/A',
    };

    try {
      const [details] = await Location.reverseGeocodeAsync({
        latitude,
        longitude,
      });

      if (details) {
        const { formattedAddress, name, city, region, country, street } =
          details;

        const fallback = name || street || 'N/A';
        const address = formattedAddress || fallback;

        googlePlacesAutocompleteRef.current?.setAddressText(address);

        Object.assign(locationObj, {
          city: city || 'N/A',
          state: region || 'N/A',
          country: country || 'N/A',
          street: street || 'N/A',
          landmark: fallback,
          address,
        });

        const fullCoords = { ...coords, latitude, longitude } as Point;

        setCoordinates(fullCoords);
        onSelectLocation(locationObj, fullCoords);
        setLocation(locationObj);
        setLocationName(address);
      }
    } catch (error) {
      console.error('Error fetching address:', error);
    }
  };

  useEffect(() => {
    if (defaultValue?.landmark || defaultValue?.address) {
      googlePlacesAutocompleteRef.current?.setAddressText(
        defaultValue?.landmark || defaultValue.address
      );
      setIsPlacePicked(true);
    }
  }, [defaultValue]);
  
  useEffect(() => {
    handleGetLocation();
  }, []);

  useEffect(() => {
    onSelectLocation(location, coordinates);
  }, [location, coordinates, onSelectLocation]);

  return (
    <View className={className}>
      <GooglePlacesAutocomplete
        {...googlePlacesAutocompleteDefaultProps}
        // GooglePlacesDetailsQuery={detailsQuery}
        ref={googlePlacesAutocompleteRef}
        GooglePlacesSearchQuery={{}}
        predefinedPlaces={[]}
        disableScroll
        placeholder="Search Location"
        textInputProps={{
          InputComp: useBottomSheetInput ? BottomSheetTextInput : TextInput,
          placeholderTextColor: isDark ? colors.grey[60] : colors.grey[50],
          returnKeyType: 'search',
          onFocus: () => {
            setSearchFocused(true);
            setIsPlacePicked(true);
            onFocus?.();
          },
          onBlur: () => {
            setSearchFocused(false);
            setIsPlacePicked(false);
            onBlur?.();
          },
          defaultValue: locationName,
        }}
        debounce={400}
        renderLeftButton={() => (
          <View className="pl-3">
            <LocationIcon
              color={_searchFocused ? colors.brand[60] : colors.grey[50]}
              strokeColor={isDark ? colors.grey[60] : colors.grey[50]}
              height={24}
              width={24}
            />
          </View>
        )}
        renderRightButton={() => (
          <>
            {hasCurrentLocationIcon && (
              <TouchableOpacity
                onPress={handleGetLocation}
                style={{ padding: 5, paddingRight: 12 }}
              >
                <SimpleLineIcons
                  name="cursor"
                  size={20}
                  color={colors.brand[60]}
                />
              </TouchableOpacity>
            )}
          </>
        )}
        minLength={2}
        // currentLocation
        // currentLocationLabel="Current location"
        fetchDetails
        keepResultsAfterBlur={true}
        keyboardShouldPersistTaps="handled"
        enablePoweredByContainer={false}
        onPress={(_data, details = null) => {
          setCoordinates(details?.geometry.location as Point);
          extractAddressInfo(
            details?.address_components as AddressComponent[],
            details?.formatted_address as string,
            details?.name as string,
            details?.geometry.location as Point
          );
          setLocationName(details?.name as string);
          setSearchFocused(false);
          setIsPlacePicked(true);
          // googlePlacesAutocompleteRef.current?.setAddressText('');
          googlePlacesAutocompleteRef.current?.blur();
        }}
        listViewDisplayed={isPlacePicked ? false : 'auto'}
        query={{
          key: Env.GOOGLE_MAPS_PLACES_API_KEY,
          language: 'en',
          types: ['address', 'geocode', 'establishment'],
        }}
        styles={{
          container: {
            flex: 0,
            // backgroundColor: isDark ? colors.grey[90] : colors.grey[10],
          },
          textInputContainer: {
            borderWidth: _searchFocused ? 1 : 0,
            borderColor: _searchFocused ? colors.brand[60] : colors.grey[50],
            borderRadius,
            alignItems: 'center',
            backgroundColor: isDark ? colors.grey[90] : colors.grey[10],
          },
          textInput: {
            fontSize: 14,
            backgroundColor: isDark ? colors.grey[90] : colors.grey[10],
            borderRadius,
            color: colors.grey[50],
            marginBottom: 0,
            height: textInputHeight,
            paddingVertical: 8,
          },
          description: {
            color: colors.grey[50],
          },
          predefinedPlacesDescription: {
            color: '#1faadb',
          },
          separator: {
            height: 0.5,
            backgroundColor: isDark ? colors.grey[50] : colors.grey[60],
          },
          poweredContainer: {
            backgroundColor: isDark ? colors.grey[90] : colors.grey[10],
            justifyContent: 'center',
          },

          powered: {
            color: colors.grey[50],
          },
          row: {
            backgroundColor: isDark ? colors.grey[90] : colors.grey[10],
          },
          listView: {
            display: _searchFocused ? 'flex' : 'none',
            backgroundColor: isDark ? colors.grey[90] : colors.grey[10],
            zIndex: 50,
            position: 'absolute',
            top: textInputHeight + 2,
          },
        }}
        isNewPlacesAPI={false}
        renderDescription={(row) => row.description}
      />
    </View>
  );
};

export default LocationInput;
