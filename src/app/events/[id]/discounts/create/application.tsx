import { useLocalSearchParams, useRouter } from 'expo-router';
import React from 'react';
import { useFormContext } from 'react-hook-form';

import { BareLayout } from '@/components/layouts/bare-layout';
import {
  Button,
  Checkbox,
  P,
  RadioGroup,
  RadioGroupOption,
  View,
} from '@/components/ui';
import {
  cn,
  type CreateDiscountsFormType,
  usePurchaseTicketContext,
} from '@/lib';

export default function CreateDiscountsApplication() {
  const { watch, setValue } = useFormContext<CreateDiscountsFormType>();
  const router = useRouter();
  const { id, slug } = useLocalSearchParams<{
    id: string;
    slug: string;
  }>();

  const { event } = usePurchaseTicketContext();

  const ticketCategories = event?.ticketCategories || {};

  const eventTickets = Object.entries(ticketCategories).map(
    ([category, { id }]) => ({
      id,
      category,
    })
  );

  const selectedTickets = watch('tickets');

  const isForAll = watch('isForAll');

  const handleApplicationTypeChange = (value: string) => {
    if (value === 'all') {
      setValue('isForAll', true);
    } else {
      setValue('isForAll', false);
    }
  };

  return (
    <BareLayout
      title="Discount application"
      subTitle="You can apply the discount to all tickets or specific tickets."
      footer={
        <Button
          testID="go-to-limits-page-button"
          label="Next"
          className="m-4"
          disabled={isForAll ? false : !(watch('tickets')?.length > 0)}
          onPress={() =>
            router.push({
              pathname: '/events/[id]/discounts/create/limits',
              params: { id, slug },
            })
          }
        />
      }
    >
      <RadioGroup
        value={isForAll ? 'all' : 'selected'}
        onValueChange={handleApplicationTypeChange}
        className="gap-4 py-4"
      >
        <View className="gap-4">
          <RadioGroupOption
            value="all"
            currentValue={isForAll ? 'all' : 'selected'}
            title="Apply to all tickets"
            onPress={() => {
              handleApplicationTypeChange('all');
            }}
            labelClassName={cn(
              !isForAll && 'text-fg-muted-light dark:text-fg-muted-dark'
            )}
          />
          <View className="rounded-md bg-bg-subtle-light dark:bg-bg-subtle-dark">
            <RadioGroupOption
              value="selected"
              currentValue={isForAll ? 'all' : 'selected'}
              title="Apply to specific ticket(s)"
              labelClassName={cn(
                isForAll && 'text-fg-muted-light dark:text-fg-muted-dark'
              )}
            />
            {!isForAll && (
              // Need to show and set tickets
              <>
                {!isForAll && (
                  <View className="gap-2 rounded-md p-4">
                    {eventTickets.map((ticket) => {
                      const isChecked = selectedTickets?.includes(
                        ticket.category
                      );

                      return (
                        <View
                          key={ticket.id}
                          className="flex-row items-center justify-between rounded-md border border-border-subtle-light p-4 dark:border-border-subtle-dark"
                        >
                          <P>{ticket.category}</P>
                          <Checkbox
                            checked={isChecked}
                            onChange={() => {
                              let updatedTickets = [...(selectedTickets || [])];

                              if (isChecked) {
                                updatedTickets = updatedTickets.filter(
                                  (category) => category !== ticket.category
                                );
                              } else {
                                updatedTickets.push(ticket.category);
                              }

                              setValue('tickets', updatedTickets);
                            }}
                            accessibilityLabel="Select ticket"
                          />
                        </View>
                      );
                    })}
                  </View>
                )}
              </>
            )}
          </View>
        </View>
      </RadioGroup>
    </BareLayout>
  );
}
